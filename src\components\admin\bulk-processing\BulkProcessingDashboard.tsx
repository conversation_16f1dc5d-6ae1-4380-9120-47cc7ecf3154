'use client';

import { useState } from 'react';
import { FileUploadSection } from './FileUploadSection';
import { ManualEntrySection } from './ManualEntrySection';
import { ProcessingOptionsPanel } from './ProcessingOptionsPanel';
import { ProgressTracker } from './ProgressTracker';
import { ResultsViewer } from './ResultsViewer';
import { BulkJobHistory } from './BulkJobHistory';
import { BulkProcessingJob } from '@/lib/types';
import { BulkProcessingOptions } from '@/lib/bulk-processing/bulk-engine';

interface BulkProcessingDashboardProps {
  jobs: BulkProcessingJob[];
  loading: boolean;
  onJobUpdate: () => void;
  onError: (error: string) => void;
}

type InputMethod = 'file' | 'manual' | 'none';
type ProcessingStep = 'input' | 'configure' | 'processing' | 'results';

/**
 * Bulk Processing Dashboard
 * 
 * Main orchestrator component for bulk processing operations.
 * Manages the workflow from input to results with step-by-step navigation.
 */
export function BulkProcessingDashboard({
  jobs,
  loading,
  onJobUpdate,
  onError,
}: BulkProcessingDashboardProps) {
  const [currentStep, setCurrentStep] = useState<ProcessingStep>('input');
  const [inputMethod, setInputMethod] = useState<InputMethod>('none');
  const [processedData, setProcessedData] = useState<any>(null);
  const [processingOptions, setProcessingOptions] = useState<BulkProcessingOptions>({
    batchSize: 5,
    delayBetweenBatches: 2000,
    retryAttempts: 3,
    aiProvider: 'openai',
    skipExisting: true,
    scrapeOnly: false,
    generateContent: true,
    autoPublish: false,
    priority: 'normal',
  });
  const [activeJobId, setActiveJobId] = useState<string | null>(null);

  const handleInputMethodSelect = (method: InputMethod) => {
    setInputMethod(method);
    setProcessedData(null);
  };

  const handleDataProcessed = (data: any) => {
    setProcessedData(data);
    setCurrentStep('configure');
  };

  const handleOptionsChange = (options: Partial<BulkProcessingOptions>) => {
    setProcessingOptions(prev => ({ ...prev, ...options }));
  };

  const handleStartProcessing = async () => {
    if (!processedData) {
      onError('No data to process');
      return;
    }

    try {
      setCurrentStep('processing');
      
      const response = await fetch('/api/admin/bulk-processing', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-admin-api-key': process.env.NEXT_PUBLIC_ADMIN_API_KEY || '',
        },
        body: JSON.stringify({
          type: 'processed_data',
          data: processedData.validItems,
          options: processingOptions,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to start bulk processing');
      }

      const result = await response.json();
      if (result.success) {
        setActiveJobId(result.jobId);
        onJobUpdate();
      } else {
        throw new Error(result.error || 'Unknown error');
      }
    } catch (err) {
      onError(err instanceof Error ? err.message : 'Failed to start processing');
      setCurrentStep('configure');
    }
  };

  const handleJobComplete = () => {
    setCurrentStep('results');
    onJobUpdate();
  };

  const handleReset = () => {
    setCurrentStep('input');
    setInputMethod('none');
    setProcessedData(null);
    setActiveJobId(null);
  };

  const stepIndicators = [
    { key: 'input', label: 'Input Data', icon: '📁' },
    { key: 'configure', label: 'Configure', icon: '⚙️' },
    { key: 'processing', label: 'Processing', icon: '🔄' },
    { key: 'results', label: 'Results', icon: '📊' },
  ];

  return (
    <div className="space-y-6">
      {/* Step Indicator */}
      <div className="bg-zinc-800 border border-black rounded-lg p-6">
        <div className="flex items-center justify-between">
          {stepIndicators.map((step, index) => (
            <div key={step.key} className="flex items-center">
              <div className={`
                flex items-center justify-center w-10 h-10 rounded-full border-2 transition-colors
                ${currentStep === step.key 
                  ? 'bg-orange-500 border-orange-500 text-white' 
                  : stepIndicators.findIndex(s => s.key === currentStep) > index
                    ? 'bg-green-500 border-green-500 text-white'
                    : 'bg-zinc-700 border-zinc-600 text-gray-400'
                }
              `}>
                <span className="text-sm">{step.icon}</span>
              </div>
              <div className="ml-3">
                <div className={`font-medium ${
                  currentStep === step.key ? 'text-orange-400' : 'text-gray-400'
                }`}>
                  {step.label}
                </div>
              </div>
              {index < stepIndicators.length - 1 && (
                <div className={`w-16 h-0.5 mx-4 ${
                  stepIndicators.findIndex(s => s.key === currentStep) > index
                    ? 'bg-green-500'
                    : 'bg-zinc-600'
                }`} />
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Step Content */}
      {currentStep === 'input' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <FileUploadSection
            active={inputMethod === 'file'}
            onSelect={() => handleInputMethodSelect('file')}
            onDataProcessed={handleDataProcessed}
            onError={onError}
          />
          <ManualEntrySection
            active={inputMethod === 'manual'}
            onSelect={() => handleInputMethodSelect('manual')}
            onDataProcessed={handleDataProcessed}
            onError={onError}
          />
        </div>
      )}

      {currentStep === 'configure' && processedData && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2">
            <ProcessingOptionsPanel
              options={processingOptions}
              onChange={handleOptionsChange}
              dataPreview={processedData}
              onStartProcessing={handleStartProcessing}
              onBack={() => setCurrentStep('input')}
            />
          </div>
          <div>
            <ResultsViewer
              data={processedData}
              mode="preview"
            />
          </div>
        </div>
      )}

      {currentStep === 'processing' && activeJobId && (
        <ProgressTracker
          jobId={activeJobId}
          onComplete={handleJobComplete}
          onError={onError}
        />
      )}

      {currentStep === 'results' && (
        <div className="space-y-6">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-bold">Processing Complete</h2>
            <button
              onClick={handleReset}
              className="bg-orange-500 hover:bg-orange-600 px-4 py-2 rounded-lg font-medium transition-colors"
            >
              Start New Batch
            </button>
          </div>
          <ResultsViewer
            jobId={activeJobId}
            mode="results"
          />
        </div>
      )}

      {/* Job History */}
      <BulkJobHistory
        jobs={jobs}
        loading={loading}
        onJobSelect={(jobId) => {
          setActiveJobId(jobId);
          setCurrentStep('results');
        }}
      />
    </div>
  );
}
