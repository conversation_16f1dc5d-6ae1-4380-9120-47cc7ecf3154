/**
 * Persistent Data Storage for Scrape.do Integration
 * Handles saving scraped content as .md files for future AI processing
 */

import { promises as fs } from 'fs';
import { join, dirname } from 'path';
import { 
  StorageResult, 
  ScrapedDataRecord, 
  DataStorageConfig, 
  EnhancedScrapeResult 
} from './types';

export class DataStorage {
  private config: DataStorageConfig;

  constructor(config?: Partial<DataStorageConfig>) {
    this.config = {
      baseDirectory: join(process.cwd(), 'data', 'scraped-content'),
      fileFormat: 'markdown',
      includeMetadata: true,
      organizationStrategy: 'by_domain',
      maxFileSize: 10 * 1024 * 1024, // 10MB
      compressionEnabled: false,
      ...config
    };
    
    this.ensureBaseDirectory();
  }

  /**
   * Store scraped content as markdown file for AI processing
   */
  async storeScrapedContent(result: EnhancedScrapeResult): Promise<StorageResult> {
    try {
      const record: ScrapedDataRecord = {
        url: result.url || '',
        content: result.content,
        mediaAssets: result.mediaAssets,
        additionalPages: result.additionalPages,
        contentAnalysis: result.contentAnalysis,
        costAnalysis: result.costAnalysis,
        scrapedAt: result.timestamp,
        storedAt: new Date().toISOString()
      };

      const filePath = await this.generateFilePath(record.url);
      const content = this.formatAsMarkdown(record);

      // Check file size
      if (Buffer.byteLength(content, 'utf8') > this.config.maxFileSize) {
        return {
          success: false,
          error: `Content exceeds maximum file size (${this.config.maxFileSize} bytes)`,
          timestamp: new Date().toISOString()
        };
      }

      // Ensure directory exists
      await fs.mkdir(dirname(filePath), { recursive: true });

      // Write file
      await fs.writeFile(filePath, content, 'utf8');

      return {
        success: true,
        filePath,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        success: false,
        error: (error as Error).message,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Format scraped data as markdown for AI consumption
   */
  private formatAsMarkdown(record: ScrapedDataRecord): string {
    const lines: string[] = [];

    // Header with metadata
    if (this.config.includeMetadata) {
      lines.push('---');
      lines.push(`url: ${record.url}`);
      lines.push(`scraped_at: ${record.scrapedAt}`);
      lines.push(`stored_at: ${record.storedAt}`);
      
      if (record.costAnalysis) {
        lines.push(`credits_used: ${record.costAnalysis.creditsUsed}`);
        lines.push(`optimization_strategy: ${record.costAnalysis.optimizationStrategy}`);
      }
      
      if (record.contentAnalysis) {
        lines.push(`content_scenario: ${record.contentAnalysis.scenario || 'unknown'}`);
        lines.push(`content_quality: ${record.contentAnalysis.confidence}%`);
      }
      
      if (record.mediaAssets) {
        if (record.mediaAssets.favicon) {
          lines.push(`favicon: ${record.mediaAssets.favicon.length > 0 ? 'available' : 'none'}`);
        }
        lines.push(`og_images: ${record.mediaAssets.ogImages.length}`);
        lines.push(`screenshot: ${record.mediaAssets.screenshot ? 'available' : 'none'}`);
      }
      
      lines.push('---');
      lines.push('');
    }

    // Main content
    lines.push('# Main Content');
    lines.push('');
    lines.push(record.content);

    // Additional pages
    if (record.additionalPages && record.additionalPages.length > 0) {
      lines.push('');
      lines.push('---');
      lines.push('');
      lines.push('# Additional Pages');
      lines.push('');

      record.additionalPages.forEach((page, index) => {
        lines.push(`## Page ${index + 1}: ${page.url || 'Unknown URL'}`);
        lines.push('');
        if (page.metadata?.pageType) {
          lines.push(`**Page Type**: ${page.metadata.pageType}`);
          lines.push('');
        }
        lines.push(page.content);
        lines.push('');
      });
    }

    // Media assets information
    if (record.mediaAssets && this.config.includeMetadata) {
      lines.push('');
      lines.push('---');
      lines.push('');
      lines.push('# Media Assets');
      lines.push('');

      if (record.mediaAssets.favicon && record.mediaAssets.favicon.length > 0) {
        lines.push('## Favicon');
        record.mediaAssets.favicon.forEach(favicon => {
          lines.push(`- ${favicon}`);
        });
        lines.push('');
      }

      if (record.mediaAssets.ogImages.length > 0) {
        lines.push('## Open Graph Images');
        record.mediaAssets.ogImages.forEach(image => {
          lines.push(`- **${image.type}**: ${image.url}`);
          if (image.metadata?.width && image.metadata?.height) {
            lines.push(`  - Dimensions: ${image.metadata.width}x${image.metadata.height}`);
          }
        });
        lines.push('');
      }

      if (record.mediaAssets.screenshot) {
        lines.push('## Screenshot');
        lines.push(`- Status: ${record.mediaAssets.screenshot.success ? 'Available' : 'Failed'}`);
        if (record.mediaAssets.screenshot.metadata) {
          const meta = record.mediaAssets.screenshot.metadata;
          lines.push(`- Dimensions: ${meta.width}x${meta.height}`);
          lines.push(`- Full Page: ${meta.fullPage ? 'Yes' : 'No'}`);
          lines.push(`- Captured: ${meta.capturedAt}`);
        }
        lines.push('');
      }
    }

    return lines.join('\n');
  }

  /**
   * Generate file path based on organization strategy
   */
  private async generateFilePath(url: string): Promise<string> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    let filename: string;
    let directory: string;

    try {
      const urlObj = new URL(url);
      const domain = urlObj.hostname.replace(/[^a-zA-Z0-9.-]/g, '_');
      const path = urlObj.pathname.replace(/[^a-zA-Z0-9.-]/g, '_').substring(0, 50);
      
      filename = `${domain}${path}_${timestamp}.md`;
      
      switch (this.config.organizationStrategy) {
        case 'by_domain':
          directory = join(this.config.baseDirectory, domain);
          break;
        case 'by_date':
          const date = new Date().toISOString().split('T')[0];
          directory = join(this.config.baseDirectory, date);
          break;
        case 'flat':
        default:
          directory = this.config.baseDirectory;
          break;
      }
    } catch {
      // Invalid URL, use fallback naming
      filename = `unknown_${timestamp}.md`;
      directory = this.config.baseDirectory;
    }

    return join(directory, filename);
  }

  /**
   * Ensure base directory exists
   */
  private async ensureBaseDirectory(): Promise<void> {
    try {
      await fs.access(this.config.baseDirectory);
    } catch {
      await fs.mkdir(this.config.baseDirectory, { recursive: true });
    }
  }

  /**
   * Retrieve stored content by URL pattern
   */
  async findStoredContent(urlPattern: string): Promise<ScrapedDataRecord[]> {
    try {
      const records: ScrapedDataRecord[] = [];
      await this.searchDirectory(this.config.baseDirectory, urlPattern, records);
      return records;
    } catch (error) {
      console.error('Failed to find stored content:', error);
      return [];
    }
  }

  /**
   * Recursively search directory for matching files
   */
  private async searchDirectory(
    directory: string, 
    urlPattern: string, 
    results: ScrapedDataRecord[]
  ): Promise<void> {
    try {
      const entries = await fs.readdir(directory, { withFileTypes: true });
      
      for (const entry of entries) {
        const fullPath = join(directory, entry.name);
        
        if (entry.isDirectory()) {
          await this.searchDirectory(fullPath, urlPattern, results);
        } else if (entry.isFile() && entry.name.endsWith('.md')) {
          const content = await fs.readFile(fullPath, 'utf8');
          const record = this.parseMarkdownRecord(content);
          
          if (record && record.url.includes(urlPattern)) {
            results.push(record);
          }
        }
      }
    } catch (error) {
      // Skip directories that can't be read
    }
  }

  /**
   * Parse markdown file back to ScrapedDataRecord
   */
  private parseMarkdownRecord(content: string): ScrapedDataRecord | null {
    try {
      const lines = content.split('\n');
      const metadata: Record<string, string> = {};
      let contentStart = 0;

      // Parse frontmatter
      if (lines[0] === '---') {
        for (let i = 1; i < lines.length; i++) {
          if (lines[i] === '---') {
            contentStart = i + 1;
            break;
          }
          const [key, ...valueParts] = lines[i].split(':');
          if (key && valueParts.length > 0) {
            metadata[key.trim()] = valueParts.join(':').trim();
          }
        }
      }

      const mainContent = lines.slice(contentStart).join('\n');

      return {
        url: metadata.url || '',
        content: mainContent,
        scrapedAt: metadata.scraped_at || '',
        storedAt: metadata.stored_at || ''
      };
    } catch {
      return null;
    }
  }

  /**
   * Get storage statistics
   */
  async getStorageStats(): Promise<{
    totalFiles: number;
    totalSize: number;
    oldestFile?: string;
    newestFile?: string;
    byDomain: Record<string, number>;
  }> {
    try {
      const stats = {
        totalFiles: 0,
        totalSize: 0,
        oldestFile: undefined as string | undefined,
        newestFile: undefined as string | undefined,
        byDomain: {} as Record<string, number>
      };

      await this.collectStats(this.config.baseDirectory, stats);
      return stats;
    } catch {
      return { totalFiles: 0, totalSize: 0, byDomain: {} };
    }
  }

  /**
   * Recursively collect storage statistics
   */
  private async collectStats(
    directory: string, 
    stats: any
  ): Promise<void> {
    try {
      const entries = await fs.readdir(directory, { withFileTypes: true });
      
      for (const entry of entries) {
        const fullPath = join(directory, entry.name);
        
        if (entry.isDirectory()) {
          await this.collectStats(fullPath, stats);
        } else if (entry.isFile() && entry.name.endsWith('.md')) {
          const fileStats = await fs.stat(fullPath);
          stats.totalFiles++;
          stats.totalSize += fileStats.size;
          
          // Track oldest/newest
          if (!stats.oldestFile || fileStats.mtime < (await fs.stat(stats.oldestFile)).mtime) {
            stats.oldestFile = fullPath;
          }
          if (!stats.newestFile || fileStats.mtime > (await fs.stat(stats.newestFile)).mtime) {
            stats.newestFile = fullPath;
          }
          
          // Track by domain (extract from filename)
          const domain = entry.name.split('_')[0];
          stats.byDomain[domain] = (stats.byDomain[domain] || 0) + 1;
        }
      }
    } catch {
      // Skip directories that can't be read
    }
  }

  /**
   * Clean up old files
   */
  async cleanupOldFiles(maxAgeMs: number = 90 * 24 * 60 * 60 * 1000): Promise<number> {
    try {
      const deletedCount = 0;
      await this.deleteOldFiles(this.config.baseDirectory, maxAgeMs, deletedCount);
      return deletedCount;
    } catch {
      return 0;
    }
  }

  /**
   * Recursively delete old files
   */
  private async deleteOldFiles(
    directory: string, 
    maxAgeMs: number, 
    deletedCount: { value: number }
  ): Promise<void> {
    try {
      const entries = await fs.readdir(directory, { withFileTypes: true });
      const now = Date.now();
      
      for (const entry of entries) {
        const fullPath = join(directory, entry.name);
        
        if (entry.isDirectory()) {
          await this.deleteOldFiles(fullPath, maxAgeMs, deletedCount);
        } else if (entry.isFile() && entry.name.endsWith('.md')) {
          const stats = await fs.stat(fullPath);
          if (now - stats.mtime.getTime() > maxAgeMs) {
            await fs.unlink(fullPath);
            deletedCount.value++;
          }
        }
      }
    } catch {
      // Skip directories that can't be processed
    }
  }
}

// Export singleton instance
export const dataStorage = new DataStorage();
