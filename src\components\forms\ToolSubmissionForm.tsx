'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { apiClient } from '@/lib/api';
import { Button } from '@/components/ui/Button';

// Validation schema
const toolSubmissionSchema = z.object({
  name: z.string().min(1, 'Tool name is required').max(100, 'Tool name must be less than 100 characters'),
  url: z.string().url('Please enter a valid URL'),
  description: z.string().min(50, 'Description must be at least 50 characters').max(500, 'Description must be less than 500 characters'),
  category: z.string().min(1, 'Please select a category'),
  subcategory: z.string().optional(),
  submitterName: z.string().min(1, 'Your name is required').max(100, 'Name must be less than 100 characters'),
  submitterEmail: z.string().email('Please enter a valid email address'),
  pricingType: z.string().min(1, 'Please select a pricing type'),
});

type ToolSubmissionData = z.infer<typeof toolSubmissionSchema>;

interface ToolSubmissionFormProps {
  onSuccess?: () => void;
  onCancel?: () => void;
}

export function ToolSubmissionForm({ onSuccess, onCancel }: ToolSubmissionFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch
  } = useForm<ToolSubmissionData>({
    resolver: zodResolver(toolSubmissionSchema),
    mode: 'onChange'
  });

  const categories = [
    { value: 'writing-tools', label: 'Writing Tools' },
    { value: 'image-generators', label: 'Image Generators' },
    { value: 'chatbots', label: 'Chatbots' },
    { value: 'dev-tools', label: 'Developer Tools' },
    { value: 'video-ai', label: 'Video AI' },
    { value: 'music-generation', label: 'Music Generation' },
    { value: 'voice-ai', label: 'Voice AI' },
    { value: 'data-analysis', label: 'Data Analysis' },
    { value: 'productivity-ai', label: 'Productivity AI' },
    { value: 'design-ai', label: 'Design AI' },
    { value: 'marketing-ai', label: 'Marketing AI' },
    { value: 'education-ai', label: 'Education AI' },
    { value: 'healthcare-ai', label: 'Healthcare AI' },
    { value: 'finance-ai', label: 'Finance AI' },
  ];

  const pricingTypes = [
    { value: 'free', label: 'Free' },
    { value: 'freemium', label: 'Freemium' },
    { value: 'paid', label: 'Paid' },
    { value: 'open-source', label: 'Open Source' },
  ];

  const onSubmit = async (data: ToolSubmissionData) => {
    setIsSubmitting(true);
    setSubmitError(null);

    try {
      await apiClient.submitTool(data);
      setSuccess(true);
      reset();

      if (onSuccess) {
        setTimeout(onSuccess, 2000);
      }
    } catch (err) {
      setSubmitError(err instanceof Error ? err.message : 'Failed to submit tool');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (success) {
    return (
      <div className="bg-zinc-800 p-6 rounded-lg border border-green-500">
        <div className="text-center">
          <div className="text-green-400 text-2xl mb-4">✅</div>
          <h3 className="text-xl font-bold text-white mb-2">Tool Submitted Successfully!</h3>
          <p className="text-gray-300">
            Thank you for your submission. We'll review your tool and publish it if approved.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-zinc-800 p-6 rounded-lg border border-zinc-700">
      <h2 className="text-2xl font-bold text-white mb-6">Submit a New AI Tool</h2>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {submitError && (
          <div className="bg-red-900/20 border border-red-500 rounded-lg p-4">
            <p className="text-red-400 text-sm">{submitError}</p>
          </div>
        )}

        {/* Tool Name */}
        <div>
          <label htmlFor="name" className="block text-sm font-medium text-gray-300 mb-2">
            Tool Name *
          </label>
          <input
            type="text"
            id="name"
            {...register('name')}
            className={`w-full px-3 py-2 bg-zinc-700 border rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent ${
              errors.name ? 'border-red-500' : 'border-zinc-600'
            }`}
            placeholder="Enter the name of your AI tool"
          />
          {errors.name && (
            <p className="mt-1 text-sm text-red-400">{errors.name.message}</p>
          )}
        </div>

        {/* Website URL */}
        <div>
          <label htmlFor="url" className="block text-sm font-medium text-gray-300 mb-2">
            Website URL *
          </label>
          <input
            type="url"
            id="url"
            {...register('url')}
            className={`w-full px-3 py-2 bg-zinc-700 border rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent ${
              errors.url ? 'border-red-500' : 'border-zinc-600'
            }`}
            placeholder="https://your-ai-tool.com"
          />
          {errors.url && (
            <p className="mt-1 text-sm text-red-400">{errors.url.message}</p>
          )}
        </div>

        {/* Description */}
        <div>
          <label htmlFor="description" className="block text-sm font-medium text-gray-300 mb-2">
            Description *
          </label>
          <textarea
            id="description"
            {...register('description')}
            rows={4}
            className={`w-full px-3 py-2 bg-zinc-700 border rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent ${
              errors.description ? 'border-red-500' : 'border-zinc-600'
            }`}
            placeholder="Describe what this tool does and its key features (50-500 characters)"
          />
          {errors.description && (
            <p className="mt-1 text-sm text-red-400">{errors.description.message}</p>
          )}
        </div>

        {/* Category and Pricing */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label htmlFor="category" className="block text-sm font-medium text-gray-300 mb-2">
              Category *
            </label>
            <select
              id="category"
              {...register('category')}
              className={`w-full px-3 py-2 bg-zinc-700 border rounded-md text-white focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent ${
                errors.category ? 'border-red-500' : 'border-zinc-600'
              }`}
            >
              <option value="">Select a category</option>
              {categories.map(cat => (
                <option key={cat.value} value={cat.value}>
                  {cat.label}
                </option>
              ))}
            </select>
            {errors.category && (
              <p className="mt-1 text-sm text-red-400">{errors.category.message}</p>
            )}
          </div>

          <div>
            <label htmlFor="pricingType" className="block text-sm font-medium text-gray-300 mb-2">
              Pricing Type *
            </label>
            <select
              id="pricingType"
              {...register('pricingType')}
              className={`w-full px-3 py-2 bg-zinc-700 border rounded-md text-white focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent ${
                errors.pricingType ? 'border-red-500' : 'border-zinc-600'
              }`}
            >
              <option value="">Select pricing type</option>
              {pricingTypes.map(type => (
                <option key={type.value} value={type.value}>
                  {type.label}
                </option>
              ))}
            </select>
            {errors.pricingType && (
              <p className="mt-1 text-sm text-red-400">{errors.pricingType.message}</p>
            )}
          </div>
        </div>

        {/* Subcategory */}
        <div>
          <label htmlFor="subcategory" className="block text-sm font-medium text-gray-300 mb-2">
            Subcategory <span className="text-gray-500">(Optional)</span>
          </label>
          <input
            type="text"
            id="subcategory"
            {...register('subcategory')}
            className="w-full px-3 py-2 bg-zinc-700 border border-zinc-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
            placeholder="e.g., Text-to-Image, Code Completion"
          />
        </div>

        {/* Submitter Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label htmlFor="submitterName" className="block text-sm font-medium text-gray-300 mb-2">
              Your Name *
            </label>
            <input
              type="text"
              id="submitterName"
              {...register('submitterName')}
              className={`w-full px-3 py-2 bg-zinc-700 border rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent ${
                errors.submitterName ? 'border-red-500' : 'border-zinc-600'
              }`}
              placeholder="Your full name"
            />
            {errors.submitterName && (
              <p className="mt-1 text-sm text-red-400">{errors.submitterName.message}</p>
            )}
          </div>

          <div>
            <label htmlFor="submitterEmail" className="block text-sm font-medium text-gray-300 mb-2">
              Your Email *
            </label>
            <input
              type="email"
              id="submitterEmail"
              {...register('submitterEmail')}
              className={`w-full px-3 py-2 bg-zinc-700 border rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent ${
                errors.submitterEmail ? 'border-red-500' : 'border-zinc-600'
              }`}
              placeholder="<EMAIL>"
            />
            {errors.submitterEmail && (
              <p className="mt-1 text-sm text-red-400">{errors.submitterEmail.message}</p>
            )}
          </div>
        </div>

        {/* Submission Guidelines */}
        <div className="bg-zinc-700/50 border border-zinc-600 rounded-lg p-4">
          <h4 className="text-white font-medium mb-2">📋 Submission Guidelines</h4>
          <ul className="text-sm text-gray-300 space-y-1">
            <li>• Your tool must be publicly accessible and functional</li>
            <li>• Provide accurate and detailed information</li>
            <li>• Tools are reviewed within 24-48 hours</li>
            <li>• We may contact you for additional information</li>
            <li>• Approved tools will be published to our directory</li>
          </ul>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-4 pt-4">
          <Button
            type="submit"
            disabled={isSubmitting}
            className="flex-1"
            variant="primary"
          >
            {isSubmitting ? 'Submitting Tool...' : 'Submit Tool for Review'}
          </Button>

          {onCancel && (
            <Button
              type="button"
              onClick={onCancel}
              variant="outline"
              className="px-6"
            >
              Cancel
            </Button>
          )}
        </div>
      </form>
    </div>
  );
}
