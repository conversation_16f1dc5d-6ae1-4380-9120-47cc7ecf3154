import { NextRequest, NextResponse } from 'next/server';
import { validate<PERSON><PERSON><PERSON><PERSON> } from '@/lib/auth';
import { 
  performHealthCheck, 
  AIUtils, 
  createOpenAIClient, 
  createOpenRouterClient,
  AI_SYSTEM_INFO 
} from '@/lib/ai';

export async function GET(request: NextRequest) {
  try {
    // Validate admin API key for security
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    console.log('🔍 Starting AI System Health Check...');

    // 1. Configuration Validation
    const configValidation = AIUtils.validateConfiguration();
    console.log('Configuration validation:', configValidation);

    // 2. Environment Variables Check
    const envCheck = {
      openai_key: !!process.env.OPENAI_API_KEY,
      openrouter_key: !!process.env.OPENROUTER_API_KEY,
      openai_model: process.env.OPENAI_MODEL,
      openrouter_model: process.env.OPENROUTER_MODEL,
      site_url: process.env.SITE_URL,
      primary_provider: process.env.AI_PROVIDER_PRIMARY,
      fallback_provider: process.env.AI_PROVIDER_FALLBACK,
      content_generation_enabled: process.env.CONTENT_GENERATION_ENABLED
    };

    // 3. Provider Health Check
    let healthCheck;
    try {
      healthCheck = await performHealthCheck();
      console.log('Health check completed:', healthCheck);
    } catch (error) {
      console.error('Health check failed:', error);
      healthCheck = {
        status: 'unhealthy',
        providers: {
          openai: { status: 'down' },
          openrouter: { status: 'down' }
        },
        configuration: configValidation,
        error: error instanceof Error ? error.message : String(error)
      };
    }

    // 4. Individual Provider Tests
    const providerTests = {
      openai: { available: false, error: null as string | null, latency: null as number | null },
      openrouter: { available: false, error: null as string | null, latency: null as number | null }
    };

    // Test OpenAI
    if (process.env.OPENAI_API_KEY) {
      try {
        const startTime = Date.now();
        const openaiClient = createOpenAIClient();
        const isConnected = await openaiClient.validateConnection();
        const latency = Date.now() - startTime;
        
        providerTests.openai = {
          available: isConnected,
          error: null,
          latency
        };
        console.log(`OpenAI test: ${isConnected ? 'SUCCESS' : 'FAILED'} (${latency}ms)`);
      } catch (error: any) {
        providerTests.openai = {
          available: false,
          error: error.message,
          latency: null
        };
        console.error('OpenAI test failed:', error.message);
      }
    }

    // Test OpenRouter
    if (process.env.OPENROUTER_API_KEY) {
      try {
        const startTime = Date.now();
        const openrouterClient = createOpenRouterClient();
        const isConnected = await openrouterClient.validateConnection();
        const latency = Date.now() - startTime;
        
        providerTests.openrouter = {
          available: isConnected,
          error: null,
          latency
        };
        console.log(`OpenRouter test: ${isConnected ? 'SUCCESS' : 'FAILED'} (${latency}ms)`);
      } catch (error: any) {
        providerTests.openrouter = {
          available: false,
          error: error.message,
          latency: null
        };
        console.error('OpenRouter test failed:', error.message);
      }
    }

    // 5. System Status Summary
    const systemStatus = {
      overall: 'healthy' as 'healthy' | 'degraded' | 'unhealthy',
      issues: [] as string[]
    };

    if (!configValidation.valid) {
      systemStatus.overall = 'unhealthy';
      systemStatus.issues.push('Configuration issues detected');
    }

    if (!providerTests.openai.available && !providerTests.openrouter.available) {
      systemStatus.overall = 'unhealthy';
      systemStatus.issues.push('No AI providers available');
    } else if (!providerTests.openai.available || !providerTests.openrouter.available) {
      systemStatus.overall = 'degraded';
      systemStatus.issues.push('One AI provider unavailable');
    }

    return NextResponse.json({
      success: true,
      timestamp: new Date().toISOString(),
      system: {
        status: systemStatus.overall,
        issues: systemStatus.issues,
        version: AI_SYSTEM_INFO.version,
        features: AI_SYSTEM_INFO.features
      },
      configuration: {
        valid: configValidation.valid,
        issues: configValidation.issues,
        environment: envCheck
      },
      providers: {
        openai: {
          configured: !!process.env.OPENAI_API_KEY,
          available: providerTests.openai.available,
          latency: providerTests.openai.latency,
          error: providerTests.openai.error,
          model: process.env.OPENAI_MODEL
        },
        openrouter: {
          configured: !!process.env.OPENROUTER_API_KEY,
          available: providerTests.openrouter.available,
          latency: providerTests.openrouter.latency,
          error: providerTests.openrouter.error,
          model: process.env.OPENROUTER_MODEL
        }
      },
      healthCheck,
      recommendations: generateRecommendations(configValidation, providerTests)
    });

  } catch (error: any) {
    console.error('AI health check API error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error.message,
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

function generateRecommendations(
  configValidation: any, 
  providerTests: any
): string[] {
  const recommendations = [];

  if (!configValidation.valid) {
    recommendations.push('Fix configuration issues in environment variables');
  }

  if (!providerTests.openai.available && providerTests.openai.error) {
    recommendations.push(`OpenAI issue: ${providerTests.openai.error}`);
  }

  if (!providerTests.openrouter.available && providerTests.openrouter.error) {
    recommendations.push(`OpenRouter issue: ${providerTests.openrouter.error}`);
  }

  if (providerTests.openai.available && providerTests.openrouter.available) {
    recommendations.push('All providers healthy - system ready for production');
  }

  if (providerTests.openai.latency > 5000 || providerTests.openrouter.latency > 5000) {
    recommendations.push('High latency detected - check network connectivity');
  }

  return recommendations;
}
