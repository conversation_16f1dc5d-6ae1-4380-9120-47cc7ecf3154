/**
 * Bulk Processing Engine
 * Orchestrates bulk processing operations with batch management and progress tracking
 */

import { createClient } from '@supabase/supabase-js';
import { BulkProcessingJob, BulkToolData, JobStatus } from '@/lib/types';
import { JobPriority, JobType } from '@/lib/jobs/types';
import { getJobManager } from '@/lib/jobs';
import { configManager } from '@/lib/config';

// Bulk processing interfaces
export interface BulkProcessingOptions {
  batchSize: number;
  delayBetweenBatches: number;
  retryAttempts: number;
  aiProvider: 'openai' | 'openrouter';
  skipExisting: boolean;
  scrapeOnly?: boolean;
  generateContent?: boolean;
  autoPublish?: boolean;
  priority?: 'low' | 'normal' | 'high';
}

export interface BulkJobResult {
  jobId: string;
  totalItems: number;
  processedItems: number;
  successfulItems: number;
  failedItems: number;
  results: {
    successful: Array<{
      toolId: string;
      url: string;
      generatedAt: string;
    }>;
    failed: Array<{
      url: string;
      error: string;
      timestamp: string;
    }>;
  };
  processingTime: number;
  costEstimate?: number;
}

export interface BatchResult {
  batchIndex: number;
  totalItems: number;
  successful: number;
  failed: number;
  results: Array<{
    url: string;
    success: boolean;
    toolId?: string;
    error?: string;
    processingTime: number;
  }>;
}

/**
 * Bulk Processing Engine
 * Manages bulk operations with intelligent batching and error handling
 */
export class BulkProcessingEngine {
  private supabase;
  private jobManager;
  private configManager;
  private activeJobs = new Map<string, BulkProcessingJob>();

  constructor() {
    this.supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );
    this.jobManager = getJobManager();
    this.configManager = configManager;
  }

  /**
   * Create and start a bulk processing job
   */
  async createBulkJob(
    data: BulkToolData[],
    options: BulkProcessingOptions,
    metadata: {
      jobType: 'text_file' | 'json_file' | 'manual_entry';
      filename?: string;
      submittedBy?: string;
    }
  ): Promise<BulkProcessingJob> {
    try {
      // Generate job ID
      const jobId = this.generateJobId();

      // Create bulk processing job record
      const bulkJob: BulkProcessingJob = {
        id: jobId,
        jobType: metadata.jobType,
        status: 'pending',
        totalItems: data.length,
        processedItems: 0,
        successfulItems: 0,
        failedItems: 0,
        sourceData: {
          filename: metadata.filename,
          urls: data.map(item => item.url),
          tools: data.map(item => ({
            url: item.url,
            name: item.providedData.name || undefined,
            category: item.providedData.category || undefined,
            description: item.providedData.description || undefined,
          })),
        },
        processingOptions: {
          batchSize: options.batchSize,
          delayBetweenBatches: options.delayBetweenBatches,
          retryAttempts: options.retryAttempts,
          aiProvider: options.aiProvider,
          skipExisting: options.skipExisting,
        },
        results: {
          successful: [],
          failed: [],
        },
        progressLog: [],
        createdBy: metadata.submittedBy || 'system',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      // Store in database
      await this.storeBulkJobInDatabase(bulkJob);

      // Add to active jobs
      this.activeJobs.set(jobId, bulkJob);

      // Start processing
      this.processBulkJob(jobId, data).catch(error => {
        console.error(`Bulk job ${jobId} failed:`, error);
        this.failBulkJob(jobId, error.message);
      });

      console.log(`✅ Bulk job ${jobId} created with ${data.length} items`);
      return bulkJob;
    } catch (error) {
      console.error('Failed to create bulk job:', error);
      throw error;
    }
  }

  /**
   * Process a bulk job with intelligent batching
   */
  private async processBulkJob(jobId: string, originalData?: BulkToolData[]): Promise<void> {
    const bulkJob = this.activeJobs.get(jobId);
    if (!bulkJob) {
      throw new Error(`Bulk job ${jobId} not found`);
    }

    try {
      // Update status to processing
      await this.updateBulkJobStatus(jobId, 'processing');

      // Create batches - reconstruct BulkToolData from sourceData if originalData not provided
      const toolsData = originalData || this.reconstructBulkToolData(bulkJob.sourceData);
      const batches = this.createBatches(
        toolsData,
        bulkJob.processingOptions
      );

      console.log(`🔄 Processing ${batches.length} batches for job ${jobId}`);

      // Process batches sequentially
      for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
        // Check if job was stopped or paused
        const currentJob = this.activeJobs.get(jobId);
        if (!currentJob || currentJob.status === 'cancelled' || currentJob.status === 'paused') {
          console.log(`⏸️ Bulk job ${jobId} was ${currentJob?.status || 'stopped'}`);
          break;
        }

        const batch = batches[batchIndex];
        console.log(`📦 Processing batch ${batchIndex + 1}/${batches.length} (${batch.length} items)`);

        // Process batch
        const batchResult = await this.processBatch(jobId, batchIndex, batch, bulkJob.processingOptions);

        // Update bulk job progress
        await this.updateBulkJobProgress(jobId, batchResult);

        // Add delay between batches (except for last batch)
        if (batchIndex < batches.length - 1 && bulkJob.processingOptions.delayBetweenBatches > 0) {
          await new Promise(resolve => 
            setTimeout(resolve, bulkJob.processingOptions.delayBetweenBatches)
          );
        }
      }

      // Complete the job
      await this.completeBulkJob(jobId);
      console.log(`✅ Bulk job ${jobId} completed successfully`);

    } catch (error) {
      console.error(`❌ Bulk job ${jobId} failed:`, error);
      await this.failBulkJob(jobId, (error as Error).message);
      throw error;
    }
  }

  /**
   * Create batches from tool data
   */
  private createBatches(tools: BulkToolData[], options: BulkProcessingOptions): BulkToolData[][] {
    const batches: BulkToolData[][] = [];
    const batchSize = Math.max(1, Math.min(options.batchSize, 20)); // Ensure reasonable batch size

    for (let i = 0; i < tools.length; i += batchSize) {
      batches.push(tools.slice(i, i + batchSize));
    }

    return batches;
  }

  /**
   * Process a single batch
   */
  private async processBatch(
    jobId: string,
    batchIndex: number,
    batch: BulkToolData[],
    options?: BulkProcessingOptions
  ): Promise<BatchResult> {
    const results: BatchResult['results'] = [];
    let successful = 0;
    let failed = 0;

    // Process items in batch with controlled concurrency
    const maxConcurrent = 2; // Limit concurrent processing to avoid overwhelming APIs
    const chunks = this.chunkArray(batch, maxConcurrent);

    for (const chunk of chunks) {
      const chunkPromises = chunk.map(async (tool) => {
        const startTime = Date.now();
        
        try {
          // Create individual job for this tool
          const individualJob = await this.jobManager.createJob(
            JobType.TOOL_PROCESSING,
            {
              url: tool.url,
              providedData: tool.providedData,
              needsGeneration: tool.needsGeneration,
              bulkJobId: jobId,
              batchIndex,
            },
            {
              priority: this.mapPriorityToJobPriority(options?.priority || 'normal'),
            }
          );

          // Wait for job completion (with timeout)
          const result = await this.waitForJobCompletion(individualJob.id, 300000); // 5 minute timeout

          if (result.success) {
            successful++;
            return {
              url: tool.url,
              success: true,
              toolId: result.toolId,
              processingTime: Date.now() - startTime,
            };
          } else {
            failed++;
            return {
              url: tool.url,
              success: false,
              error: result.error || 'Unknown error',
              processingTime: Date.now() - startTime,
            };
          }
        } catch (error) {
          failed++;
          return {
            url: tool.url,
            success: false,
            error: (error as Error).message,
            processingTime: Date.now() - startTime,
          };
        }
      });

      const chunkResults = await Promise.allSettled(chunkPromises);
      results.push(...chunkResults.map(r => 
        r.status === 'fulfilled' ? r.value : {
          url: 'unknown',
          success: false,
          error: 'Promise rejected',
          processingTime: 0,
        }
      ));
    }

    return {
      batchIndex,
      totalItems: batch.length,
      successful,
      failed,
      results,
    };
  }

  /**
   * Wait for individual job completion
   */
  private async waitForJobCompletion(jobId: string, timeoutMs: number): Promise<{
    success: boolean;
    toolId?: string;
    error?: string;
  }> {
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeoutMs) {
      try {
        const job = await this.jobManager.getJob(jobId);

        if (!job) {
          return {
            success: false,
            error: 'Job not found',
          };
        }

        if (job.status === 'completed') {
          return {
            success: true,
            toolId: job.result?.toolId,
          };
        }

        if (job.status === 'failed') {
          return {
            success: false,
            error: job.error || 'Job failed',
          };
        }
        
        // Wait before checking again
        await new Promise(resolve => setTimeout(resolve, 2000));
      } catch (error) {
        return {
          success: false,
          error: (error as Error).message,
        };
      }
    }
    
    return {
      success: false,
      error: 'Job timeout',
    };
  }

  /**
   * Utility function to chunk array
   */
  private chunkArray<T>(array: T[], chunkSize: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  }

  /**
   * Map priority string to JobPriority enum
   */
  private mapPriorityToJobPriority(priority: string): JobPriority {
    switch (priority) {
      case 'low': return JobPriority.LOW;
      case 'high': return JobPriority.HIGH;
      default: return JobPriority.NORMAL;
    }
  }

  /**
   * Generate unique job ID
   */
  private generateJobId(): string {
    return `bulk_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Reconstruct BulkToolData from sourceData for processing
   */
  private reconstructBulkToolData(sourceData: BulkProcessingJob['sourceData']): BulkToolData[] {
    return (sourceData.urls || []).map(url => ({
      url,
      providedData: {
        name: null,
        category: null,
        description: null,
        pricing: null,
        features: null,
      },
      needsGeneration: {
        name: true,
        description: true,
        features: true,
        pricing: true,
        prosAndCons: true,
        haiku: true,
        hashtags: true,
      },
    }));
  }

  /**
   * Store bulk job in database
   */
  private async storeBulkJobInDatabase(job: BulkProcessingJob): Promise<void> {
    const jobData = {
      id: job.id,
      job_type: job.jobType,
      status: job.status,
      total_items: job.totalItems,
      processed_items: job.processedItems,
      successful_items: job.successfulItems,
      failed_items: job.failedItems,
      source_data: job.sourceData,
      processing_options: job.processingOptions,
      results: job.results,
      progress_log: job.progressLog,
      created_by: job.createdBy,
      created_at: job.createdAt,
      updated_at: job.updatedAt,
    };

    const { error } = await this.supabase
      .from('bulk_processing_jobs')
      .insert(jobData);

    if (error) {
      console.error('Failed to store bulk job in database:', error);
      throw error;
    }
  }

  /**
   * Update bulk job status
   */
  private async updateBulkJobStatus(jobId: string, status: JobStatus): Promise<void> {
    const job = this.activeJobs.get(jobId);
    if (job) {
      job.status = status;
      job.updatedAt = new Date().toISOString();
    }

    const { error } = await this.supabase
      .from('bulk_processing_jobs')
      .update({
        status,
        updated_at: new Date().toISOString(),
      })
      .eq('id', jobId);

    if (error) {
      console.error(`Failed to update bulk job ${jobId} status:`, error);
      throw error;
    }
  }

  /**
   * Update bulk job progress
   */
  private async updateBulkJobProgress(jobId: string, batchResult: BatchResult): Promise<void> {
    const job = this.activeJobs.get(jobId);
    if (!job) return;

    // Update counters
    job.processedItems += batchResult.totalItems;
    job.successfulItems += batchResult.successful;
    job.failedItems += batchResult.failed;
    job.updatedAt = new Date().toISOString();

    // Update results
    batchResult.results.forEach(result => {
      if (result.success && result.toolId) {
        job.results.successful.push({
          toolId: result.toolId,
          url: result.url,
          generatedAt: new Date().toISOString(),
        });
      } else {
        job.results.failed.push({
          url: result.url,
          error: result.error || 'Unknown error',
          timestamp: new Date().toISOString(),
        });
      }
    });

    // Update in database
    const { error } = await this.supabase
      .from('bulk_processing_jobs')
      .update({
        processed_items: job.processedItems,
        successful_items: job.successfulItems,
        failed_items: job.failedItems,
        results: job.results,
        updated_at: job.updatedAt,
      })
      .eq('id', jobId);

    if (error) {
      console.error(`Failed to update bulk job ${jobId} progress:`, error);
      throw error;
    }
  }

  /**
   * Complete bulk job
   */
  private async completeBulkJob(jobId: string): Promise<void> {
    const job = this.activeJobs.get(jobId);
    if (!job) return;

    job.status = 'completed';
    job.completedAt = new Date().toISOString();
    job.updatedAt = new Date().toISOString();

    const { error } = await this.supabase
      .from('bulk_processing_jobs')
      .update({
        status: 'completed',
        completed_at: job.completedAt,
        updated_at: job.updatedAt,
      })
      .eq('id', jobId);

    if (error) {
      console.error(`Failed to complete bulk job ${jobId}:`, error);
      throw error;
    }

    // Remove from active jobs
    this.activeJobs.delete(jobId);
  }

  /**
   * Fail bulk job
   */
  private async failBulkJob(jobId: string, errorMessage: string): Promise<void> {
    const job = this.activeJobs.get(jobId);
    if (!job) return;

    job.status = 'failed';
    // Add error to progress log instead of a separate error field
    job.progressLog.push({
      timestamp: new Date().toISOString(),
      message: errorMessage,
      level: 'error'
    });
    job.updatedAt = new Date().toISOString();

    const { error } = await this.supabase
      .from('bulk_processing_jobs')
      .update({
        status: 'failed',
        error_message: errorMessage,
        updated_at: job.updatedAt,
      })
      .eq('id', jobId);

    if (error) {
      console.error(`Failed to update bulk job ${jobId} failure:`, error);
    }

    // Remove from active jobs
    this.activeJobs.delete(jobId);
  }

  /**
   * Get bulk job status
   */
  async getBulkJob(jobId: string): Promise<BulkProcessingJob | null> {
    // Check active jobs first
    const activeJob = this.activeJobs.get(jobId);
    if (activeJob) {
      return activeJob;
    }

    // Query database
    const { data, error } = await this.supabase
      .from('bulk_processing_jobs')
      .select('*')
      .eq('id', jobId)
      .single();

    if (error || !data) {
      return null;
    }

    return this.mapDatabaseToBulkJob(data);
  }

  /**
   * Get all bulk jobs with filtering
   */
  async getBulkJobs(filter: {
    status?: JobStatus;
    limit?: number;
    offset?: number;
  } = {}): Promise<BulkProcessingJob[]> {
    let query = this.supabase
      .from('bulk_processing_jobs')
      .select('*')
      .order('created_at', { ascending: false });

    if (filter.status) {
      query = query.eq('status', filter.status);
    }

    if (filter.limit) {
      query = query.limit(filter.limit);
    }

    if (filter.offset) {
      query = query.range(filter.offset, (filter.offset + (filter.limit || 10)) - 1);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Failed to fetch bulk jobs:', error);
      return [];
    }

    return (data || []).map(this.mapDatabaseToBulkJob);
  }

  /**
   * Pause bulk job
   */
  async pauseBulkJob(jobId: string): Promise<void> {
    await this.updateBulkJobStatus(jobId, 'paused');
    console.log(`⏸️ Bulk job ${jobId} paused`);
  }

  /**
   * Resume bulk job
   */
  async resumeBulkJob(jobId: string): Promise<void> {
    const job = await this.getBulkJob(jobId);
    if (!job || job.status !== 'paused') {
      throw new Error(`Cannot resume job ${jobId}: job not found or not paused`);
    }

    // Add back to active jobs and resume processing
    this.activeJobs.set(jobId, job);
    await this.updateBulkJobStatus(jobId, 'processing');

    // Resume processing from where it left off
    this.processBulkJob(jobId).catch(error => {
      console.error(`Resumed bulk job ${jobId} failed:`, error);
      this.failBulkJob(jobId, error.message);
    });

    console.log(`▶️ Bulk job ${jobId} resumed`);
  }

  /**
   * Cancel bulk job
   */
  async cancelBulkJob(jobId: string): Promise<void> {
    await this.updateBulkJobStatus(jobId, 'cancelled');
    this.activeJobs.delete(jobId);
    console.log(`⏹️ Bulk job ${jobId} cancelled`);
  }

  /**
   * Map database record to BulkProcessingJob
   */
  private mapDatabaseToBulkJob(data: any): BulkProcessingJob {
    return {
      id: data.id,
      jobType: data.job_type,
      status: data.status,
      totalItems: data.total_items,
      processedItems: data.processed_items,
      successfulItems: data.successful_items,
      failedItems: data.failed_items,
      sourceData: data.source_data,
      processingOptions: data.processing_options,
      results: data.results,
      progressLog: data.progress_log || [],
      createdBy: data.created_by || data.submitted_by || 'system',
      createdAt: data.created_at,
      updatedAt: data.updated_at,
      completedAt: data.completed_at,
    };
  }
}

// Singleton instance
let bulkProcessingEngine: BulkProcessingEngine | null = null;

export function getBulkProcessingEngine(): BulkProcessingEngine {
  if (!bulkProcessingEngine) {
    bulkProcessingEngine = new BulkProcessingEngine();
  }
  return bulkProcessingEngine;
}
